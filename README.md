# 電子書轉檔工具

本工具提供簡易圖形介面，整合 Calibre 的 ebook-convert，支援多種主流電子書格式互相轉換。

## 功能特色
- 支援格式：epub, pdf, mobi, azw3, txt, html, docx, fb2, cbz, cbr
- 一鍵選擇檔案、格式並轉檔
- 轉檔進度與結果提示

## 安裝需求
1. **Python 3.x**（建議 3.7 以上）
2. **Calibre**（https://calibre-ebook.com/）
   - 安裝後請將 `ebook-convert` 加入系統環境變數（PATH）
3. **tkinter**（Python 內建，通常無需額外安裝）

## 使用方式
1. 安裝 Calibre 並確認 `ebook-convert` 可在命令列執行：
   ```
   ebook-convert --version
   ```
2. 下載本專案程式碼。
3. 於命令列執行：
   ```
   python main.py
   ```
4. 依照介面操作，選擇輸入檔案、輸出格式與輸出檔案路徑，點擊「開始轉檔」。

## 注意事項
- 轉檔品質與支援度取決於 Calibre 的 ebook-convert。
- 若遇到格式不支援或轉檔失敗，請參考 Calibre 官方說明。

## 支援格式
- epub
- pdf
- mobi
- azw3
- txt
- html
- docx
- fb2
- cbz
- cbr

---
如有問題歡迎回報！ 