import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import os

# 支援的格式
SUPPORTED_FORMATS = [
    'epub', 'pdf', 'mobi', 'azw3', 'txt', 'html', 'docx', 'fb2', 'cbz', 'cbr'
]

def convert_ebook(input_path, output_path, output_format, status_label):
    try:
        status_label.config(text='轉檔中...')
        status_label.update()
        cmd = [
            'ebook-convert',
            input_path,
            output_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            status_label.config(text='轉檔完成！')
            messagebox.showinfo('成功', '轉檔完成！')
        else:
            status_label.config(text='轉檔失敗')
            messagebox.showerror('錯誤', f'轉檔失敗：\n{result.stderr}')
    except Exception as e:
        status_label.config(text='轉檔失敗')
        messagebox.showerror('錯誤', f'發生例外：{e}')

def select_input_file(entry):
    file_path = filedialog.askopenfilename()
    if file_path:
        entry.delete(0, tk.END)
        entry.insert(0, file_path)

def select_output_file(entry, output_format):
    file_path = filedialog.asksaveasfilename(defaultextension=f'.{output_format}',
                                             filetypes=[(f'{output_format.upper()} files', f'*.{output_format}'), ('All files', '*.*')])
    if file_path:
        entry.delete(0, tk.END)
        entry.insert(0, file_path)

def main():
    root = tk.Tk()
    root.title('電子書轉檔工具')
    root.geometry('480x260')

    # 輸入檔案
    tk.Label(root, text='選擇輸入檔案：').grid(row=0, column=0, padx=10, pady=10, sticky='e')
    input_entry = tk.Entry(root, width=40)
    input_entry.grid(row=0, column=1, padx=5)
    tk.Button(root, text='瀏覽', command=lambda: select_input_file(input_entry)).grid(row=0, column=2, padx=5)

    # 輸出格式
    tk.Label(root, text='選擇輸出格式：').grid(row=1, column=0, padx=10, pady=10, sticky='e')
    format_var = tk.StringVar(value=SUPPORTED_FORMATS[0])
    format_menu = ttk.Combobox(root, textvariable=format_var, values=SUPPORTED_FORMATS, state='readonly', width=10)
    format_menu.grid(row=1, column=1, sticky='w')

    # 輸出檔案
    tk.Label(root, text='指定輸出檔案：').grid(row=2, column=0, padx=10, pady=10, sticky='e')
    output_entry = tk.Entry(root, width=40)
    output_entry.grid(row=2, column=1, padx=5)
    def on_select_output():
        fmt = format_var.get()
        select_output_file(output_entry, fmt)
    tk.Button(root, text='瀏覽', command=on_select_output).grid(row=2, column=2, padx=5)

    # 狀態顯示
    status_label = tk.Label(root, text='')
    status_label.grid(row=4, column=0, columnspan=3, pady=10)

    # 轉檔按鈕
    def on_convert():
        input_path = input_entry.get()
        output_path = output_entry.get()
        output_format = format_var.get()
        if not input_path or not os.path.isfile(input_path):
            messagebox.showwarning('警告', '請選擇正確的輸入檔案！')
            return
        if not output_path:
            messagebox.showwarning('警告', '請指定輸出檔案！')
            return
        convert_ebook(input_path, output_path, output_format, status_label)

    tk.Button(root, text='開始轉檔', command=on_convert, width=20).grid(row=3, column=0, columnspan=3, pady=10)

    root.mainloop()

if __name__ == '__main__':
    main() 